import { createFormHook, createFormHookContexts } from "@tanstack/react-form";
import EffiCurrency from "./components/FormInputFields/AdvancedCompositions/EffiCurrency";
import EffiPercentageField from "./components/FormInputFields/AdvancedCompositions/EffiPercentageField";
import EffiDate from "./components/FormInputFields/EffiDate";
import EffiMultiSelect from "./components/FormInputFields/EffiMultiSelect";
import EffiRadioGroup from "./components/FormInputFields/EffiRadioGroup";
import EffiSelect from "./components/FormInputFields/EffiSelect";
import EffiSwitch from "./components/FormInputFields/EffiSwitch";
import EffiTextField from "./components/FormInputFields/EffiTextField";

export const { fieldContext, formContext, useFieldContext } = createFormHookContexts();

export const { useAppForm } = createFormHook({
  fieldContext,
  formContext,
  fieldComponents: {
    EffiTextField,
    EffiSelect,
    EffiDate,
    EffiSwitch,
    EffiPercentageField,
    EffiCurrency,
    EffiMultiSelect,
    EffiRadioGroup,
  },
  formComponents: {},
});
